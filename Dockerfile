FROM acrdodo.azurecr.io/superset-frontend:latest as frontend

FROM apache/superset:4.1.1

USER root

# Install requirements
COPY src/requirements /app/requirements/
RUN pip install --upgrade pip
RUN --mount=type=cache,target=/root/.cache/pip \
    apt-get update -qq && apt-get install -yqq --no-install-recommends \
      build-essential pkg-config \
    && pip install -r /app/requirements/requirements.txt \
    && apt-get autoremove -yqq --purge build-essential pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Add configuration
COPY src/configs/ /app/configs/

# Add bootstrap commands
COPY src/docker /usr/bin/

# Add plugins
COPY src/superset/ /app/superset/

RUN rm -rf /app/superset/static/assets/
RUN rm -rf /app/superset-frontend/

COPY --from=frontend /app/superset/static/assets/ /app/superset/static/assets/
COPY --from=frontend /app/superset-frontend/ /app/superset-frontend/

USER superset
