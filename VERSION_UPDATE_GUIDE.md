# Руководство по обновлению версии Superset

## Обзор

Система версионности в superset-plugins позволяет отображать актуальную версию приложения во фронтенде Apache Superset. Версия передается через backend конфигурацию и отображается в интерфейсе пользователя.

## Как обновить версию

### 1. Обновление основной версии

Для обновления версии приложения отредактируйте файл `version.json` в корне репозитория:

```json
{
  "version": "4.1.1_7",  // Новая версия
  "build_date": "2025-01-19",
  "description": "Dodo Pizza Superset Custom Build"
}
```

### 2. Автоматическая генерация версии

При каждой сборке автоматически генерируется файл `version_info.json` со следующей информацией:

- `version` - версия из version.json
- `build_date` - дата и время сборки
- `GIT_SHA` - полный SHA коммита
- `git_short_sha` - короткий SHA коммита
- `build_number` - номер сборки GitHub Actions
- `workflow_run_id` - ID запуска workflow
- `description` - описание сборки

## Как работает система

### Frontend сборка (.github/workflows/build-frontend.yml)

1. Читает версию из `version.json`
2. Генерирует `version_info.json` с актуальными данными
3. Копирует файл в `repos/superset/superset/static/version_info.json`
4. Собирает фронтенд с включенной версией

### Backend сборка (.github/workflows/build-backend.yml)

1. Генерирует актуальный `version_info.json`
2. Копирует файл в `src/superset/static/version_info.json`
3. Собирает backend с актуальной версией

### Plugin сборка

Аналогично генерирует версию для плагинов (dev и prod).

## Отображение версии во фронтенде

Версия передается во фронтенд через `bootstrap_data.common.version_info` и доступна в JavaScript коде:

```javascript
// Доступ к версии в frontend коде
const versionInfo = window.bootstrap_data?.common?.version_info;
console.log('Current version:', versionInfo?.version);
console.log('Build date:', versionInfo?.build_date);
console.log('Git SHA:', versionInfo?.git_short_sha);
```

## Конфигурация Backend

В `src/configs/superset_config.py` добавлена функция `COMMON_BOOTSTRAP_OVERRIDES_FUNC`, которая:

1. Читает версию из различных источников (файл, переменные окружения)
2. Передает информацию о версии во фронтенд через bootstrap data

## Переменные окружения

Можно переопределить версию через переменные окружения:

- `SUPERSET_VERSION` - версия приложения
- `BUILD_NUMBER` - номер сборки
- `GIT_SHA` - SHA коммита

## Особенности

1. **Независимость от фронтенда**: При каждой сборке backend генерируется актуальная версия, независимо от того, когда был собран фронтенд.

2. **Fallback механизм**: Если файл версии не найден, используется версия по умолчанию.

3. **Логирование**: Все операции с версией логируются для отладки.

## Примеры использования

### Отображение версии в UI

Версию можно отобразить в любом месте интерфейса, например, в футере или в меню "О программе":

```jsx
const VersionDisplay = () => {
  const versionInfo = window.bootstrap_data?.common?.version_info;
  
  return (
    <div>
      Version: {versionInfo?.version} 
      ({versionInfo?.git_short_sha})
    </div>
  );
};
```

### Отладка

Для отладки можно проверить загруженную версию в консоли браузера:

```javascript
console.log('Version info:', window.bootstrap_data?.common?.version_info);
```
