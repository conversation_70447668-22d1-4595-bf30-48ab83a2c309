import json
import sys

# Constants
LIMIT = 65535 * 2  # The byte limit

# Helper function to convert bytes to KB
def bytes_to_kb(bytes_value):
    return round(bytes_value / 1024, 2)

# Function to recursively count items in a JSON object based on "type"
def count_items_in_position_json_recursive(json_data, types):
    item_counts = {item_type: 0 for item_type in types}

    def traverse(data):
        if isinstance(data, dict):  # If the data is a dictionary
            if "type" in data:  # Check if the dictionary has a "type" field
                item_type = data["type"]
                if item_type in types:
                    item_counts[item_type] += 1
            # Recursively traverse the "children" field if it exists
            if "children" in data and isinstance(data["children"], list):
                for child in data["children"]:
                    traverse(child)
            # Recursively traverse all other values in the dictionary
            for value in data.values():
                if isinstance(value, (dict, list)):
                    traverse(value)
        elif isinstance(data, list):  # If the data is a list
            for item in data:  # Recursively traverse each item in the list
                traverse(item)

    traverse(json_data)
    return item_counts


# Function to calculate counts of various items in position_json grouped by tabs
def count_items_by_tabs(position_json):
    try:
        if not position_json:  # Skip if position_json is None or empty
            return None, None

        # Handle escaped JSON strings
        if isinstance(position_json, str):
            try:
                # Try to parse the JSON directly
                json_data = json.loads(position_json)
            except json.JSONDecodeError:
                # If it fails, assume it's an escaped JSON string and unescape it first
                unescaped_json = position_json.encode('utf-8').decode('unicode_escape')
                json_data = json.loads(unescaped_json)
        else:
            return None, None

        # Define types to search for
        types = {
            "CHART",   # Matches "type": "CHART"
            "ROW",     # Matches "type": "ROW"
            "HEADER",  # Matches "type": "HEADER"
            "TAB",     # Matches "type": "TAB"
            "GRID",    # Matches "type": "GRID"
            "DIVIDER", # Matches "type": "DIVIDER"
            "MARKDOWN",# Matches "type": "MARKDOWN"
            "COLUMN",  # Matches "type": "COLUMN"
            "ROOT",    # Matches "type": "ROOT"
        }

        # Dictionary to store counts per tab
        tab_counts = {}
        total_item_counts = {item_type: 0 for item_type in types}

        # Traverse each component in the JSON
        for component_id, component in json_data.items():
            if "type" in component and "parents" in component:
                # Identify the tab(s) this component belongs to
                parents = component["parents"]
                for parent in parents:
                    if parent.startswith("TABS-") or parent.startswith("TAB-"):
                        tab_id = parent
                        if tab_id not in tab_counts:
                            tab_counts[tab_id] = {item_type: 0 for item_type in types}
                        item_type = component["type"]
                        if item_type in tab_counts[tab_id]:
                            tab_counts[tab_id][item_type] += 1
                # Aggregate total item counts
                item_type = component["type"]
                if item_type in total_item_counts:
                    total_item_counts[item_type] += 1

        return tab_counts, total_item_counts

    except (json.JSONDecodeError, TypeError):
        # Skip invalid JSON or None values without showing any message
        return None, None


# Function to calculate the size of position_json in bytes
def calculate_position_json_size(position_json):
    try:
        if not position_json:  # Skip if position_json is None or empty
            return None

        # Handle escaped JSON strings
        if isinstance(position_json, str):
            try:
                # Try to parse the JSON directly
                json_data = json.loads(position_json)
            except json.JSONDecodeError:
                # If it fails, assume it's an escaped JSON string and unescape it first
                unescaped_json = position_json.encode('utf-8').decode('unicode_escape')
                json_data = json.loads(unescaped_json)

        # Calculate the size in bytes
        size_in_bytes = len(json.dumps(json_data).encode('utf-8'))
        return size_in_bytes
    except (json.JSONDecodeError, TypeError):
        # Skip invalid JSON or None values without showing any message
        return None


# Helper function to format item counts
def format_item_counts(item_counts):
    return ", ".join(f"{key.title()}s: {value}" for key, value in item_counts.items() if value > 0)


# Read the JSON file
with open('./dashboards_202503050902.json', 'r', encoding='utf-8') as file:
    data = json.load(file)  # Load the entire JSON file
    dashboards = data.get("dashboards", [])  # Extract the list of dashboards


# Check if a specific dashboard ID is provided as a command-line argument
if len(sys.argv) > 1:
    target_dashboard_id = sys.argv[1]
    target_dashboard = next((d for d in dashboards if str(d.get('id')) == target_dashboard_id), None)

    if target_dashboard:
        position_json = target_dashboard.get('position_json')

        if position_json:
            # Count items by tabs and aggregate total item counts
            tab_counts, total_item_counts = count_items_by_tabs(position_json)

            # Calculate size in bytes
            size_in_bytes = calculate_position_json_size(position_json)

            if tab_counts is not None and total_item_counts is not None and size_in_bytes is not None:
                percent_remaining = ((LIMIT - size_in_bytes) / LIMIT) * 100 if size_in_bytes <= LIMIT else 0
                percent_over_limit = ((size_in_bytes - LIMIT) / LIMIT) * 100 if size_in_bytes > LIMIT else 0

                # Print dashboard details
                formatted_total_counts = format_item_counts(total_item_counts)
                size_kb = bytes_to_kb(size_in_bytes)
                limit_kb = bytes_to_kb(LIMIT)
                print(f"\nDashboard ID: {target_dashboard['id']}")
                print(f"Size: {size_kb} KB ({size_in_bytes} bytes), Remaining: {percent_remaining:.2f}% (limit {limit_kb} KB ({LIMIT} bytes))")
                print(f"  Total Items: {formatted_total_counts}")

                # Print tab details
                for tab_id, counts in tab_counts.items():
                    formatted_counts = format_item_counts(counts)
                    total_children = sum(counts.values())
                    print(f"  {tab_id} has {total_children} nested children: {formatted_counts}")

                # Calculate averages
                total_tabs = len(tab_counts)
                avg_counts = {item_type: round(sum(tab[item_type] for tab in tab_counts.values()) / total_tabs) for item_type in tab_counts[next(iter(tab_counts))]}
                formatted_avg_counts = format_item_counts(avg_counts)
                print(f"  TABS contain on average: {formatted_avg_counts}\n")
            else:
                print(f"Dashboard ID {target_dashboard_id}: Invalid or empty position_json.")
        else:
            print(f"Dashboard ID {target_dashboard_id}: No position_json available.")
    else:
        print(f"No dashboard found with ID {target_dashboard_id}.")
else:
    # Process all dashboards and categorize them into Over Limit and Remaining
    over_limit_dashboards = []
    remaining_dashboards = []

    for dashboard in dashboards:
        position_json = dashboard.get('position_json')  # Safely get the position_json field

        if position_json:  # Proceed only if position_json is not None
            # Count items by tabs and aggregate total item counts
            tab_counts, total_item_counts = count_items_by_tabs(position_json)

            # Calculate size in bytes
            size_in_bytes = calculate_position_json_size(position_json)

            if tab_counts is not None and total_item_counts is not None and size_in_bytes is not None:
                percent_remaining = ((LIMIT - size_in_bytes) / LIMIT) * 100 if size_in_bytes <= LIMIT else 0
                percent_over_limit = ((size_in_bytes - LIMIT) / LIMIT) * 100 if size_in_bytes > LIMIT else 0

                # Add to appropriate category
                if percent_over_limit > 0:
                    over_limit_dashboards.append({
                        'id': dashboard['id'],
                        'tab_counts': tab_counts,
                        'total_item_counts': total_item_counts,
                        'size_in_bytes': size_in_bytes,
                        'percent_over_limit': percent_over_limit
                    })
                elif percent_remaining > 0 and percent_remaining <= 50:  # Filter Remaining <= 50%
                    remaining_dashboards.append({
                        'id': dashboard['id'],
                        'tab_counts': tab_counts,
                        'total_item_counts': total_item_counts,
                        'size_in_bytes': size_in_bytes,
                        'percent_remaining': percent_remaining
                    })

    # Sort both lists by percentage (descending order)
    over_limit_dashboards.sort(key=lambda x: x["percent_over_limit"], reverse=True)
    remaining_dashboards.sort(key=lambda x: x["percent_remaining"], reverse=False)

    # Print the results
    print("\nDashboards Over the Limit (Sorted by Over-Limit Percentage):")
    if over_limit_dashboards:
        for dashboard in over_limit_dashboards:
            print(f"\nDashboard ID: {dashboard['id']}")
            size_kb = bytes_to_kb(dashboard['size_in_bytes'])
            limit_kb = bytes_to_kb(LIMIT)
            print(f"Size: {size_kb} KB ({dashboard['size_in_bytes']} bytes), Over Limit: {dashboard['percent_over_limit']:.2f}% (limit {limit_kb} KB ({LIMIT} bytes))")

            # Print total item counts
            formatted_total_counts = format_item_counts(dashboard['total_item_counts'])
            print(f"  Total Items: {formatted_total_counts}")

            # Print tab details
            for tab_id, counts in dashboard['tab_counts'].items():
                formatted_counts = format_item_counts(counts)
                total_children = sum(counts.values())
                print(f"  {tab_id} has {total_children} nested children: {formatted_counts}")

            # Calculate averages
            total_tabs = len(dashboard['tab_counts'])
            avg_counts = {item_type: round(sum(tab[item_type] for tab in dashboard['tab_counts'].values()) / total_tabs) for item_type in dashboard['tab_counts'][next(iter(dashboard['tab_counts']))]}
            formatted_avg_counts = format_item_counts(avg_counts)
            print(f"  TABS contain on average: {formatted_avg_counts}\n")
    else:
        print("No dashboards over the limit.")

    print("\nDashboards With Space Remaining (Filtered to <= 50% Remaining, Sorted by Remaining Percentage):")
    if remaining_dashboards:
        for dashboard in remaining_dashboards:
            print(f"\nDashboard ID: {dashboard['id']}")
            size_kb = bytes_to_kb(dashboard['size_in_bytes'])
            limit_kb = bytes_to_kb(LIMIT)
            print(f"Size: {size_kb} KB ({dashboard['size_in_bytes']} bytes), Remaining: {dashboard['percent_remaining']:.2f}% (limit {limit_kb} KB ({LIMIT} bytes))")

            # Print total item counts
            formatted_total_counts = format_item_counts(dashboard['total_item_counts'])
            print(f"  Total Items: {formatted_total_counts}")

            # Print tab details
            for tab_id, counts in dashboard['tab_counts'].items():
                formatted_counts = format_item_counts(counts)
                total_children = sum(counts.values())
                print(f"  {tab_id} has {total_children} nested children: {formatted_counts}")

            # Calculate averages
            total_tabs = len(dashboard['tab_counts'])
            avg_counts = {item_type: round(sum(tab[item_type] for tab in dashboard['tab_counts'].values()) / total_tabs) for item_type in dashboard['tab_counts'][next(iter(dashboard['tab_counts']))]}
            formatted_avg_counts = format_item_counts(avg_counts)
            print(f"  TABS contain on average: {formatted_avg_counts}\n")
    else:
        print("No dashboards with space remaining (<= 50%).")