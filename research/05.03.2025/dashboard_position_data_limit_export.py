import json
import sys
import csv

# Constants
LIMIT = 65535 * 2  # The byte limit

# Helper function to convert bytes to KB
def bytes_to_kb(bytes_value):
    return round(bytes_value / 1024, 2)

# Function to format numbers with commas and regional decimal separator
def format_number(value):
    if isinstance(value, int):
        return f"{value:,}"  # Add commas for thousands separator
    elif isinstance(value, float):
        return f"{value:,.2f}".replace('.', ',')  # Replace '.' with ',' for decimal separator
    return value  # Return non-numeric values unchanged

# Function to recursively count items in a JSON object based on "type"
def count_items_in_position_json_recursive(json_data, types):
    item_counts = {item_type: 0 for item_type in types}

    def traverse(data):
        if isinstance(data, dict):  # If the data is a dictionary
            if "type" in data:
                item_type = data["type"]
                if item_type in types:
                    item_counts[item_type] += 1
            # Recursively traverse the "children" field if it exists
            if "children" in data and isinstance(data["children"], list):
                for child in data["children"]:
                    traverse(child)
            # Recursively traverse all other values in the dictionary
            for value in data.values():
                if isinstance(value, (dict, list)):
                    traverse(value)
        elif isinstance(data, list):  # If the data is a list
            for item in data:
                traverse(item)

    traverse(json_data)
    return item_counts


# Function to calculate counts of various items in position_json grouped by tabs
def count_items_by_tabs(position_json):
    try:
        if not position_json:  # Skip if position_json is None or empty
            return None, None

        # Handle escaped JSON strings
        if isinstance(position_json, str):
            try:
                json_data = json.loads(position_json)
            except json.JSONDecodeError:
                unescaped_json = position_json.encode('utf-8').decode('unicode_escape')
                json_data = json.loads(unescaped_json)
        else:
            return None, None

        # Define types to search for
        types = {
            "CHART", "ROW", "HEADER", "TAB", "GRID", "DIVIDER", "MARKDOWN", "COLUMN",
        }

        # Dictionary to store counts per tab
        tab_counts = {}
        total_item_counts = {item_type: 0 for item_type in types}

        # Traverse each component in the JSON
        for component_id, component in json_data.items():
            if "type" in component and "parents" in component:
                parents = component["parents"]
                for parent in parents:
                    if parent.startswith("TABS-") or parent.startswith("TAB-"):
                        tab_id = parent
                        if tab_id not in tab_counts:
                            tab_counts[tab_id] = {item_type: 0 for item_type in types}
                        item_type = component["type"]
                        if item_type in tab_counts[tab_id]:
                            tab_counts[tab_id][item_type] += 1
                # Aggregate total item counts
                item_type = component["type"]
                if item_type in total_item_counts:
                    total_item_counts[item_type] += 1

        return tab_counts, total_item_counts

    except (json.JSONDecodeError, TypeError):
        return None, None


# Function to calculate the size of position_json in bytes
def calculate_position_json_size(position_json):
    try:
        if not position_json:  # Skip if position_json is None or empty
            return None

        # Handle escaped JSON strings
        if isinstance(position_json, str):
            try:
                json_data = json.loads(position_json)
            except json.JSONDecodeError:
                unescaped_json = position_json.encode('utf-8').decode('unicode_escape')
                json_data = json.loads(unescaped_json)

        # Calculate the size in bytes
        size_in_bytes = len(json.dumps(json_data).encode('utf-8'))
        return size_in_bytes
    except (json.JSONDecodeError, TypeError):
        return None


# Read the JSON file
with open('./dashboards_202503050902.json', 'r', encoding='utf-8') as file:
    data = json.load(file)  # Load the entire JSON file
    dashboards = data.get("dashboards", [])

# Process all dashboards and categorize them into Over Limit and Remaining
over_limit_dashboards = []
remaining_dashboards = []

for dashboard in dashboards:
    position_json = dashboard.get('position_json')  # Safely get the position_json field

    if position_json:  # Proceed only if position_json is not None
        # Count items by tabs and aggregate total item counts
        tab_counts, total_item_counts = count_items_by_tabs(position_json)

        # Calculate size in bytes
        size_in_bytes = calculate_position_json_size(position_json)

        if tab_counts is not None and total_item_counts is not None and size_in_bytes is not None:
            percent_remaining = ((LIMIT - size_in_bytes) / LIMIT) * 100

            # Determine if the dashboard is over or under the limit
            if size_in_bytes > LIMIT:
                over_limit_dashboards.append({
                    'id': dashboard['id'],
                    'size_kb': bytes_to_kb(size_in_bytes),
                    'total_items': total_item_counts,
                    'tab_counts': tab_counts,
                    'percent_over_limit': abs(percent_remaining / 100)  # Positive % for over-limit
                })
            else:
                remaining_dashboards.append({
                    'id': dashboard['id'],
                    'size_kb': bytes_to_kb(size_in_bytes),
                    'total_items': total_item_counts,
                    'tab_counts': tab_counts,
                    'percent_remaining': -abs(percent_remaining / 100)  # Negative % for under-limit
                })

# Sort remaining dashboards by % remaining (ascending order)
remaining_dashboards.sort(key=lambda x: x['percent_remaining'], reverse=True)
over_limit_dashboards.sort(key=lambda x: x['percent_over_limit'], reverse=False)

# Combine over-limit and remaining dashboards for final output
all_dashboards = over_limit_dashboards + remaining_dashboards

# Save results to a CSV file
csv_filename = "dashboard_analysis.csv"
with open(csv_filename, mode="w", newline="", encoding="utf-8") as csvfile:
    fieldnames = [
        "Dashboard ID",
        "Size (KB)",
        "Headers",
        "Markdowns",
        "Charts",
        "Rows",
        "Tabs",
        "Grids",
        "Dividers",
        "Columns",
        "% (-Remaining|+Over limit)"
    ]
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

    # Write header row
    writer.writerow({field: field for field in fieldnames})

    # Write the limit information
    writer.writerow({
        "Dashboard ID": "Limit (KB)",
        "Size (KB)": format_number(bytes_to_kb(LIMIT)),
        "Headers": "",
        "Markdowns": "",
        "Charts": "",
        "Rows": "",
        "Tabs": "",
        "Grids": "",
        "Dividers": "",
        "Columns": "",
        "% (-Remaining|+Over limit)": ""
    })

    # Write all dashboards
    for dashboard in all_dashboards:
        row = {
            "Dashboard ID": dashboard['id'],
            "Size (KB)": format_number(dashboard['size_kb']),
            "Headers": format_number(dashboard['total_items'].get("HEADER", 0)),
            "Markdowns": format_number(dashboard['total_items'].get("MARKDOWN", 0)),
            "Charts": format_number(dashboard['total_items'].get("CHART", 0)),
            "Rows": format_number(dashboard['total_items'].get("ROW", 0)),
            "Tabs": format_number(dashboard['total_items'].get("TAB", 0)),
            "Grids": format_number(dashboard['total_items'].get("GRID", 0)),
            "Dividers": format_number(dashboard['total_items'].get("DIVIDER", 0)),
            "Columns": format_number(dashboard['total_items'].get("COLUMN", 0)),
            "% (-Remaining|+Over limit)": format_number(dashboard.get('percent_remaining', dashboard.get('percent_over_limit')))
        }
        writer.writerow(row)

print(f"Results saved to {csv_filename}")