#####################################################################################################################
# This file should live in a separate from Superset infrastructure folder, because we don't know in                  #
# which order imports are done! In other case, it will break ggevent monkey patching and lead us to infinite         #
# recursive calls.                                                                                                  #
#####################################################################################################################

import logging

from gunicorn.glogging import Logger
from prometheus_client import Counter, Gauge, Histogram

# Instrumentation constants
METRIC_VAR = "metric"
VALUE_VAR = "value"
MTYPE_VAR = "mtype"
GAUGE_TYPE = "gauge"
COUNTER_TYPE = "counter"
HISTOGRAM_TYPE = "histogram"

# Histogram buckets for request durations (in milliseconds)
REQUEST_DURATION_BUCKETS = [
    5000, 15000, 30000, 45000, 60000, 90000, 120000, 150000, 180000
]


class GunicornPrometheusLogger(Logger):
    """statsD-based instrumentation, that passes as a logger"""

    def __init__(self, cfg):
        """host, port: statsD server"""
        Logger.__init__(self, cfg)
        self.prefix = getattr(cfg, "statsd_prefix", "superset")

        self._request_duration = Histogram(
            f"{self.prefix}_request_duration",
            "Histogram of HTTP request durations in milliseconds",
            labelnames=["endpoint", "status"],
            buckets=REQUEST_DURATION_BUCKETS,
        )

        # Define Prometheus metrics
        self._counter = Counter(
            f"{self.prefix}_counter",
            "Counter metric for Gunicorn Superset",
            labelnames=["key"],
        )

        self._gauge = Gauge(
            f"{self.prefix}_gauge",
            "Gauge metric for Gunicorn Superset",
            labelnames=["key"],
            multiprocess_mode="livesum",
        )

        self._histogram = Histogram(
            f"{self.prefix}_histogram",
            "Histogram metric for Gunicorn Superset",
            labelnames=["key"],
        )

        self._query_errors = Counter(
            f"{self.prefix}_query_errors",
            "Counter for Gunicorn query errors",
            labelnames=["endpoint", "status", "error_type"],
        )

    # Log errors and warnings
    def critical(self, msg, *args, **kwargs):
        Logger.critical(self, msg, *args, **kwargs)
        self.increment("gunicorn.log.critical", 1)

    def error(self, msg, *args, **kwargs):
        Logger.error(self, msg, *args, **kwargs)
        self.increment("gunicorn.log.error", 1)

    def warning(self, msg, *args, **kwargs):
        Logger.warning(self, msg, *args, **kwargs)
        self.increment("gunicorn.log.warning", 1)

    def exception(self, msg, *args, **kwargs):
        Logger.exception(self, msg, *args, **kwargs)
        self.increment("gunicorn.log.exception", 1)

    # Special treatment for info, the most common log level
    def info(self, msg, *args, **kwargs):
        self.log(logging.INFO, msg, *args, **kwargs)

    # skip the run-of-the-mill logs
    def debug(self, msg, *args, **kwargs):
        self.log(logging.DEBUG, msg, *args, **kwargs)

    def log(self, lvl, msg, *args, **kwargs):
        """Log a given statistic if metric, value and type are present"""
        try:
            extra = kwargs.get("extra", None)
            if extra is not None:
                metric = extra.get(METRIC_VAR, None)
                value = extra.get(VALUE_VAR, None)
                typ = extra.get(MTYPE_VAR, None)
                if metric and value and typ:
                    if typ == GAUGE_TYPE:
                        self.gauge(metric, value)
                    elif typ == COUNTER_TYPE:
                        self.increment(metric, value)
                    elif typ == HISTOGRAM_TYPE:
                        self.histogram(metric, value)
            # Always call the parent logger unless explicitly suppressed
            Logger.log(self, lvl, msg or "No message", *args, **kwargs)
        except Exception:
            Logger.warning(self, "Failed to log to statsd", exc_info=True)

    # access logging
    def access(self, resp, req, environ, request_time):
        """
        Log HTTP request metrics including duration and errors.

        Args:
            resp: Gunicorn response object.
            req: Gunicorn request object.
            environ: WSGI environment.
            request_time: Time taken for the request (time.delta object).
        """
        Logger.access(self, resp, req, environ, request_time)

        if not hasattr(request_time, 'seconds') or not hasattr(request_time, 'microseconds'):
            Logger.warning(self, "Invalid request_time object", exc_info=True)
            return

        # Calculate request duration in milliseconds
        duration_in_ms = (
            request_time.seconds * 1000 + float(request_time.microseconds) / 10**3
        )

        # Extract HTTP status code
        status = resp.status
        http_method = getattr(req, "method", "UNKNOWN")
        endpoint = getattr(req, "path", "/unknown")
        error_type = None

        if isinstance(status, str):
            status = int(status.split(None, 1)[0])

        if 400 <= status < 500:
            error_type = "client_error"
        elif 500 <= status < 600:
            error_type = "server_error"

        # Log metrics
        self.histogram("gunicorn.request.duration", duration_in_ms)
        self.increment("gunicorn.requests", 1)
        self.increment("gunicorn.request.status.%d" % status, 1)

        # Record request duration
        self._request_duration.labels(
            endpoint=endpoint,
            status=str(status),
        ).observe(duration_in_ms)

        # Used in PrometheusStatsLogger
        if error_type:
            self.log_query_error(
                error_type=error_type,
                endpoint=endpoint,
                status=status,
            )

    def log_query_error(
        self,
        error_type: str,
        endpoint: str = "/unknown",
        status: int = None
    ) -> None:
        """
        Log query errors using the _query_errors counter.

        Args:
            error_type (str): Type of error (e.g., "client_error", "server_error").
            endpoint (str): Requested endpoint (e.g., "/api/data").
            status (int): HTTP status code (e.g., 404, 500).
        """
        self._query_errors.labels(
            endpoint=endpoint,
            status=str(status),
            error_type=error_type,
        ).inc()

    # statsD methods
    # you can use those directly if you want
    def gauge(self, name, value, labels=None):
        self._gauge.labels(key=name).set(value)

    def increment(self, name, value, labels=None):
        self._counter.labels(key=name).inc(value)

    def histogram(self, name, value, labels=None):
        self._histogram.labels(key=name).observe(value)
