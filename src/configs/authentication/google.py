from utils import get_env_variable


GOOGLE_OAUTH_PROVIDER = {
    "name": "google",
    "label": "Google",
    "icon": "fa-google",
    "token_key": "access_token",
    "whitelist": ["@dodopizza.com", "@dodobrands.io", "<EMAIL>"],
    "remote_app": {
        "client_id": get_env_variable("GOOGLE_KEY"),
        "client_secret": get_env_variable("GOOGLE_SECRET"),
        "api_base_url": "https://www.googleapis.com/oauth2/v2/",
        "client_kwargs": {"scope": "email profile"},
        "request_token_url": None,
        "access_token_url": "https://accounts.google.com/o/oauth2/token",
        "authorize_url": "https://accounts.google.com/o/oauth2/auth",
    },
}
