# Superset Configuration file
# add file superset_config.py to PYTHONPATH for usage
import importlib
import json
import logging
import os
import sys
from datetime import timed<PERSON>ta
import flask
from flask import g
from confluent_kafka import Producer
from sqlalchemy.pool import QueuePool

from utils import get_env_variable, is_development

logger = logging.getLogger(__name__)
logger.addHandler(logging.StreamHandler())

# Function to read version information
def _try_read_version_info():
    """Try to read version information from various sources."""
    version_info = {
        "version": "4.1.1_6",  # Default version
        "build_date": None,
        "git_sha": None,
        "build_number": None,
        "description": "Dodo Pizza Superset Custom Build"
    }

    # Try to read from version_info.json in static folder
    version_info_paths = [
        "/app/superset/static/version_info.json",  # Docker path
        os.path.join(os.path.dirname(__file__), "../..", "version_info.json"),  # Relative path
        "version_info.json"  # Current directory
    ]

    for path in version_info_paths:
        try:
            if os.path.exists(path):
                with open(path, 'r') as f:
                    file_info = json.load(f)
                    version_info.update(file_info)
                    logger.info(f"Loaded version info from {path}: {version_info}")
                    break
        except Exception as e:
            logger.warning(f"Failed to read version info from {path}: {e}")

    # Try to read from environment variables
    if os.environ.get("SUPERSET_VERSION"):
        version_info["version"] = os.environ.get("SUPERSET_VERSION")
    if os.environ.get("BUILD_NUMBER"):
        version_info["build_number"] = os.environ.get("BUILD_NUMBER")
    if os.environ.get("GIT_SHA"):
        version_info["git_sha"] = os.environ.get("GIT_SHA")

    return version_info

# Load version information
VERSION_INFO = _try_read_version_info()

REDIS_HOST = get_env_variable("REDIS_HOST")
REDIS_PORT = get_env_variable("REDIS_PORT")
REDIS_PASSWORD = get_env_variable("REDIS_PASSWORD")
REDIS_RATELIMIT_DB = get_env_variable("REDIS_RATELIMIT_DB", 0)

# Metadata database
SQLALCHEMY_DATABASE_URI = get_env_variable("SQLALCHEMY_DATABASE_URI")

SQLALCHEMY_ENGINE_OPTIONS = {
    "poolclass": QueuePool,
    "pool_size": 30,
    "max_overflow": 20,
    "pool_recycle": 120,
    "connect_args": {"connect_timeout": 5},
    "hide_parameters": True
}

SUPERSET_DASHBOARD_POSITION_DATA_LIMIT = 65535*4

# Set this API key to enable Mapbox visualizations
# MAPBOX_API_KEY = get_env_variable("MAPBOX_API_KEY", "mapbox-api-key")
MAPBOX_API_KEY = get_env_variable("MAPBOX_API_KEY", "pk.eyJ1Ijoia2F6YWswZmYiLCJhIjoiY2xnYWt1cm5iMWlnNzNlcWxkcTZ5Z2I2byJ9.goVgwvhiiqQUUWNw0RggQA")

# Securing Session data
SECRET_KEY = get_env_variable("SECRET_KEY")
GLOBAL_ASYNC_QUERIES_JWT_SECRET = get_env_variable("SECRET_KEY")

# This will make sure the redirect_uri is properly computed, even with SSL offloading
ENABLE_PROXY_FIX = True

LOGO_TARGET_PATH = "/superset/welcome/"

FEATURE_FLAGS = {
    "ALERT_REPORTS": True,
    "DASHBOARD_RBAC": True,
    "DYNAMIC_PLUGINS": True,
    "ENABLE_TEMPLATE_PROCESSING": True,
    "GLOBAL_ASYNC_QUERIES": False,
    "SQLLAB_BACKEND_PERSISTENCE": True,
    "ENABLE_JAVASCRIPT_CONTROLS": True,
    "DASHBOARD_CROSS_FILTERS": True,
    "RLS_IN_SQLLAB": True,
    "DRILL_TO_DETAIL": True,
    "TAGGING_SYSTEM": True,
    "DRILL_BY": True,
    "ALLOW_FULL_CSV_EXPORT": False,
    "DASHBOARD_NATIVE_FILTERS_SET": True,
    "SQLLAB_FORCE_RUN_ASYNC": False
}

LANGUAGES = {
    "ru": {"flag": "ru", "name": "Russian"},
    "en": {"flag": "gb", "name": "English"},
}

# Disable SWAGGER UI for production
FAB_API_SWAGGER_UI = True

# Set this to false if you don't want users to be able to request/grant
# datasource access requests from/to other users.
ENABLE_ACCESS_REQUEST = True

KAFKA_TOPIC = "superset.log.v1"
# https://github.com/Azure/azure-event-hubs-for-kafka/blob/ca9afcac342f0aed5170705516110662f37d4085/quickstart/python/producer.py#L22
# from here took the config
try:
    KAFKA_CONFIG = {
        'bootstrap.servers': get_env_variable("KAFKA_BOOTSTRAP", "test"),
        'security.protocol': 'SASL_SSL',
        'sasl.mechanism': 'PLAIN',
        'sasl.username': '$ConnectionString',
        'sasl.password': get_env_variable("KAFKA_CONNECT_STRING", "test"),
    }
    KAFKA_PRODUCER = Producer(KAFKA_CONFIG)
except Exception:
    logger.warning("Cannot take variables for kafka in PROD config")

# Import all configuration modules
superset_config_path = os.path.dirname(__file__)
modules = [
    name
    for name in os.listdir(superset_config_path)
    if os.path.isdir(f"{superset_config_path}/{name}")
]
try:
    for module in modules:
        additional_configs = importlib.import_module(module)
        this_module = sys.modules[__name__]
        logger.warning(f"module: {this_module}")

        for key in dir(additional_configs):
            if key.isupper():
                setattr(this_module, key, getattr(additional_configs, key))
except Exception:
    logger.exception("Failed to import config for %s=%s")
    raise

TIME_GRAIN_DENYLIST: list[str] = [
    'PT1S',
    'PT5S',
    'PT30S',
    'PT1M',
    'PT5M',
    'PT10M',
    'PT15M',
    'PT30M',
    'PT1H',
    'PT6H',
    'P1W',
    "P1W/1970-01-03T00:00:00Z",
    "P1W/1970-01-04T00:00:00Z"
]

CSV_EXPORT = {"encoding": "utf-8", "sep": ";"}

EXCEL_EXPORT = {"index": False}

SUPERSET_WEBSERVER_TIMEOUT = int(timedelta(minutes=2).total_seconds())
PERMANENT_SESSION_LIFETIME = int(timedelta(hours=10).total_seconds())

HTML_SANITIZATION = False

# Enable profiling of Python calls. Turn this on and append ``?_instrument=1``
# to the page to see the call stack.
PROFILING = True

TALISMAN_DEV_CONFIG = {
    "content_security_policy": {
        "default-src": ["'self'"],
        "img-src": ["'self'", "data:", "https://www.googletagmanager.com"],
        "worker-src": ["'self'", "blob:"],
        "connect-src": [
            "'self'",
            "https://api.mapbox.com",
            "https://events.mapbox.com",
            "https://firebase.googleapis.com",
            "https://firestore.googleapis.com",
            "https://securetoken.googleapis.com",
            "https://www.googleapis.com",
            "https://firebaseinstallations.googleapis.com",
            "https://www.googletagmanager.com",
            "https://www.google-analytics.com",  # Added Google Analytics
            "https://*.firebaseapp.com",
            "https://firebasedatabase.googleapis.com",
            "https://identitytoolkit.googleapis.com",
            "https://api.rollbar.com",
        ],
        "object-src": "'none'",
        "style-src": [
            "'self'",
            "'unsafe-inline'",
            "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
        ],
        "script-src": [
            "'self'",
            "'unsafe-inline'",
            "'unsafe-eval'",
            "https://www.googletagmanager.com",  # Google Tag Manager script
        ],
    },
    "content_security_policy_nonce_in": ["script-src"],
    "force_https": False,
}

CONTENT_SECURITY_POLICY = {
    "default-src": ["'self'"],
    "script-src": [
        "'self'",
        "'unsafe-inline'",  # Required for analytics initialization
        "'unsafe-eval'",  # Required for some Firebase JS libraries
        "https://apis.google.com",
        "https://www.googletagmanager.com",  # For GTM/Analytics
        "https://www.gstatic.com",  # Firebase scripts
        "https://www.firebase.com",  # Firebase core JS
        "https://www.firebaseapp.com",  # Firebase app
        "https://www.googleapis.com",  # Firebase APIs
        "https://cdn.firebase.com",  # Firebase CDN
    ],
    "style-src": ["'self'", "'unsafe-inline'"],
    "img-src": ["'self'", "data:", "https://www.gstatic.com"],
    "connect-src": [
        "'self'",
        "https://api.mapbox.com",
        "https://events.mapbox.com",
        "https://firebase.googleapis.com",  # Firebase API
        "https://firestore.googleapis.com",  # Firestore API
        "https://securetoken.googleapis.com",  # Firebase authentication
        "https://www.googleapis.com",  # General Google API
        "https://firebaseinstallations.googleapis.com",  # Firebase installations
        "https://www.googletagmanager.com",  # Google Tag Manager for analytics
        "https://*.firebaseapp.com",  # Firebase app URL
        "https://firebasedatabase.googleapis.com",  # Firebase Realtime Database API (if using Realtime DB)
        "https://identitytoolkit.googleapis.com",  # Firebase Auth API
        "https://api.rollbar.com",
    ],
    "font-src": ["'self'", "data:"],
    "frame-src": ["'self'", "https://*.firebaseapp.com"],  # Allow Firebase-related frames
}

TALISMAN_CONFIG = {
    "content_security_policy": {
        "default-src": ["'self'"],
        "img-src": ["'self'", "data:", "https://www.googletagmanager.com"],
        "worker-src": ["'self'", "blob:"],
        "connect-src": [
            "'self'",
            "https://api.mapbox.com",
            "https://events.mapbox.com",
            "https://firebase.googleapis.com",
            "https://firestore.googleapis.com",
            "https://securetoken.googleapis.com",
            "https://www.googleapis.com",
            "https://firebaseinstallations.googleapis.com",
            "https://www.googletagmanager.com",
            "https://www.google-analytics.com",  # Added Google Analytics
            "https://*.firebaseapp.com",
            "https://firebasedatabase.googleapis.com",
            "https://identitytoolkit.googleapis.com",
            "https://api.rollbar.com",
        ],
        "object-src": "'none'",
        "style-src": [
            "'self'",
            "'unsafe-inline'",
            "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
        ],
        "script-src": [
            "'self'",
            "'strict-dynamic'",
            "'unsafe-eval'",
            "https://apis.google.com",
            "https://www.gstatic.com",
            "https://www.firebase.com",
            "https://www.firebaseapp.com",
            "https://www.googleapis.com",
            "https://cdn.firebase.com",
            "https://www.googletagmanager.com",  # Google Tag Manager script
        ],
    },
    "content_security_policy_nonce_in": ["script-src"],
    "force_https": False,
}

# Function to add version info to frontend bootstrap data
def COMMON_BOOTSTRAP_OVERRIDES_FUNC(data):
    """Add version information to frontend bootstrap data."""
    data["version_info"] = VERSION_INFO
    return data

# Development environment overrides
if is_development():
    from superset_dev_config import *

    logger.info("Development environment was set.")
