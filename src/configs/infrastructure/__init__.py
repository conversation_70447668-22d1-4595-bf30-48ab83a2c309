from .cache import CACHE_CONFIG, DATA_CACHE_CONFIG, RESULTS_BACKEND, GL<PERSON><PERSON>L_ASYNC_QUERIES_REDIS_CONFIG
from .worker import CeleryConfig
from .logging import DodoLoggingConfigurator
from .metrics import PrometheusStatsLogger, DashboardActivityLogger, metrics_middleware

CELERY_CONFIG = CeleryConfig

LOGGING_CONFIGURATOR = DodoLoggingConfigurator()

STATS_LOGGER = PrometheusStatsLogger()
EVENT_LOGGER = DashboardActivityLogger()

ADDITIONAL_MIDDLEWARE = [
    metrics_middleware,
]
