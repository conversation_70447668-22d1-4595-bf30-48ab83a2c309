import json
import logging
from typing import Optional, Any

from flask import current_app
from prometheus_client import (
    CollectorRegistry,
    multiprocess,
    generate_latest,
    CONTENT_TYPE_LATEST,
)
from sqlalchemy.exc import SQLAlchemyError
from superset.prometheus_stats_logger import PrometheusStatsLogger
from superset.utils.log import AbstractEventLogger
from werkzeug.middleware.dispatcher import DispatcherMiddleware

from utils import send2kafka # Import send2kafka from utils

logger = logging.getLogger(__name__)

class DashboardActivityLogger(AbstractEventLogger):
    """Event logger with user activity that commits logs to Superset DB"""

    def log(  # pylint: disable=too-many-arguments,too-many-locals
        self,
        user_id: Optional[int],
        action: str,
        dashboard_id: Optional[int],
        duration_ms: Optional[int],
        slice_id: Optional[int],
        referrer: Optional[str],
        *args: Any,
        **kwargs: Any,
    ) -> None:
        from superset.models.core import Log
        # from superset.models.dashboard import Dashboard
        # from superset.models.slice import Slice
        # from superset.models.user_info import UserInfo
        # from superset.models.team import Team, team_users
        # from flask_appbuilder.security.sqla.models import User
        #
        #
        # def send2kafka(session, num_of_records):
        #     try:
        #         messages = (session.query(
        #             Log.id,
        #             User.first_name,
        #             User.last_name,
        #             User.email,
        #             Dashboard.dashboard_title,
        #             Dashboard.dashboard_title_RU,
        #             Slice.slice_name,
        #             Slice.slice_name_RU,
        #             Log.json,
        #             Log.dttm,
        #             Log.duration_ms,
        #             Log.referrer,
        #             Log.dashboard_id,
        #             Log.slice_id,
        #             Team.slug,
        #             Team.name,
        #             Log.is_plugin,
        #             UserInfo.data_auth_dodo,
        #             UserInfo.language,
        #             UserInfo.country_name,
        #             Log.action,
        #         ).join(
        #             User, User.id == Log.user_id, isouter=True
        #         ).join(
        #             Slice, Slice.id == Log.slice_id, isouter=True
        #         ).join(
        #             Dashboard, Dashboard.id == Log.dashboard_id, isouter=True
        #         ).join(
        #             UserInfo, User.id == UserInfo.user_id, isouter=True
        #         ).join(
        #             team_users, Log.user_id == team_users.c.user_id, isouter=True
        #         ).join(
        #             Team, Team.id == team_users.c.team_id, isouter=True
        #         ).order_by(Log.id.desc()).limit(num_of_records).all())
        #         producer = current_app.config["KAFKA_PRODUCER"]
        #         result_list = []
        #         for message in messages:
        #             d_message = {
        #             'log_id': message[0],
        #             'first_name': message[1],
        #             'last_name': message[2],
        #             'email': message[3],
        #             'dashboard_title': message[4],
        #             'dashboard_title_RU': message[5],
        #             'slice_name': message[6],
        #             'slice_name_RU': message[7],
        #             'json': message[8],
        #             'dttm': message[9].isoformat(),
        #             'duration_ms': message[10],
        #             'referrer': message[11],
        #             'dashboard_id': message[12],
        #             'slice_id': message[13],
        #             'slug': message[14],
        #             'name': message[15],
        #             'is_plugin': message[16],
        #             'data_auth_dodo': message[17],
        #             'language': message[18],
        #             'country_name': message[19],
        #             'action': message[20],
        #             }
        #             result_list.append(d_message)
        #             if len(result_list) == KAFKA_LIMIT:
        #                 bytes_message = json.dumps(result_list).encode()
        #                 producer.produce(current_app.config["KAFKA_TOPIC"], value=bytes_message)
        #                 producer.flush()
        #                 result_list = []
        #         else:
        #             if result_list:
        #                 bytes_message = json.dumps(result_list).encode()
        #                 producer.produce(current_app.config["KAFKA_TOPIC"], value=bytes_message)
        #                 producer.flush()
        #     except KafkaError as e:
        #         logger.error(f"KafkaError: {e}")
        #     except Exception as e:
        #         logger.error(e)
        #         logger.error("Cant send message to kafka")

        records = kwargs.get("records", [])
        logs = []

        for record in records:
            json_string: Optional[str] = None
            try:
                json_string = json.dumps(record)
            except Exception as e:
                logger.warning(f"[metrics.py] Failed to serialize record to JSON: {e}. Record: {record}")

            is_plugin = "https://officemanager" in referrer if referrer and isinstance(referrer, str) else False

            # is_plugin = None
            # if referrer:
            #     if "https://officemanager" in referrer:
            #         is_plugin = True
            #     elif "https://analytics.dodois.io" in referrer:
            #         is_plugin = False
            #     elif "https://superset.d.yandex.dodois.dev" in referrer:
            #         is_plugin = False
            log = Log(
                action=action,
                json=json_string,
                dashboard_id=dashboard_id,
                slice_id=slice_id,
                duration_ms=duration_ms,
                referrer=referrer,
                user_id=user_id,
                is_plugin=is_plugin,
            )
            logs.append(log)

            # Used in PrometheusStatsLogger
            if dashboard_id is not None:
                self.stats_logger.incr(f"dashboard_{dashboard_id}")

            try:
                # Used in PrometheusStatsLogger
                if duration_ms is not None and dashboard_id is not None:
                    self.stats_logger.duration(
                        dashboard_id=dashboard_id,
                        is_plugin=is_plugin,
                        duration_ms=duration_ms,
                    )

            except Exception as e:
                logger.error(f"[metrics.py] Failed to log to Prometheus: {e}")

        # Persist logs to database and send to Kafka
        try:
            sesh = current_app.appbuilder.get_session
            sesh.bulk_save_objects(logs)
            sesh.commit()
            send2kafka(sesh, len(logs))  # Use send2kafka from kafka.py
        except SQLAlchemyError as ex:
            logger.error("[metrics.py] DashboardActivityLogger failed to log event(s): %s", str(ex), exc_info=True)

    @property
    def stats_logger(self) -> PrometheusStatsLogger:
        return current_app.config["STATS_LOGGER"]


def metrics_middleware(app):
    middleware = DispatcherMiddleware(app, {"/metrics": _make_multiprocess_wsgi_app()})
    return middleware


def _make_multiprocess_wsgi_app():
    def prometheus_app(environ, start_response):
        registry = CollectorRegistry()
        multiprocess.MultiProcessCollector(registry)
        data = generate_latest(registry)
        status = "200 OK"
        response_headers = [
            ("Content-type", CONTENT_TYPE_LATEST),
            ("Content-Length", str(len(data))),
        ]
        start_response(status, response_headers)
        return iter([data])

    return prometheus_app
