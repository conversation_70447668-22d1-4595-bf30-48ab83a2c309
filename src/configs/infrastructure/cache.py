from cachelib.redis import RedisCache

from utils import get_env_variable

REDIS_HOST = get_env_variable("REDIS_HOST")
REDIS_PORT = get_env_variable("REDIS_PORT")
REDIS_PASSWORD = get_env_variable("REDIS_PASSWORD")

REDIS_METADATA_DB = get_env_variable("REDIS_METADATA_DB", 0)
REDIS_METADATA_CACHE_PREFIX = "superset-metadata-"

REDIS_RESULTS_DB = get_env_variable("REDIS_RESULTS_DB", 0)
REDIS_RESULTS_CACHE_PREFIX = "superset-results-"

REDIS_FILTER_STATE_DB = get_env_variable("REDIS_FILTER_STATE_DB", 0)
REDIS_FILTER_STATE_CACHE_PREFIX = "superset-filter_state-"

REDIS_EXPLORE_FORM_DATA_DB = get_env_variable("REDIS_EXPLORE_FORM_DATA_DB", 0)
REDIS_EXPLORE_FORM_DATA_CACHE_PREFIX = "superset-explore_form_data-"

# Caching Queries
CACHE_CONFIG = {
    "CACHE_TYPE": "RedisCache",  # Specify the cache type
    "CACHE_KEY_PREFIX": REDIS_METADATA_CACHE_PREFIX,  # The key prefix for the cache values stored on the server
    "CACHE_REDIS_URL": f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_METADATA_DB}",
}

# Caching Results
DATA_CACHE_CONFIG = {
    "CACHE_TYPE": "RedisCache",  # Specify the cache type
    "CACHE_KEY_PREFIX": REDIS_RESULTS_CACHE_PREFIX,  # The key prefix for the cache values stored on the server
    "CACHE_DEFAULT_TIMEOUT": 60 * 60 * 24,  # 1 day default (in secs)
    "CACHE_REDIS_URL": f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_RESULTS_DB}",
}

# Cache for dashboard filter state. `CACHE_TYPE` defaults to `SupersetMetastoreCache`
# that stores the values in the key-value table in the Superset metastore, as it's
# required for Superset to operate correctly, but can be replaced by any
# `Flask-Caching` backend.
FILTER_STATE_CACHE_CONFIG = {
    "CACHE_TYPE": "SupersetMetastoreCache",
    "CACHE_DEFAULT_TIMEOUT": 60 * 60 * 24,  # 1 day default (in secs)
    # Should the timeout be reset when retrieving a cached value?
    "REFRESH_TIMEOUT_ON_RETRIEVAL": True,
    "CACHE_REDIS_URL": f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_RESULTS_DB}",
}

# Cache for explore form data state. `CACHE_TYPE` defaults to `SupersetMetastoreCache`
# that stores the values in the key-value table in the Superset metastore, as it's
# required for Superset to operate correctly, but can be replaced by any
# `Flask-Caching` backend.
EXPLORE_FORM_DATA_CACHE_CONFIG = {
    "CACHE_TYPE": "SupersetMetastoreCache",
    "CACHE_DEFAULT_TIMEOUT": 7 * 60 * 60 * 24,  # 7 day default (in secs)
    # Should the timeout be reset when retrieving a cached value?
    "REFRESH_TIMEOUT_ON_RETRIEVAL": True,
}

# Persisting results from running query handling using Celery workers
RESULTS_BACKEND = RedisCache(
    host=REDIS_HOST,
    port=REDIS_PORT,
    password=REDIS_PASSWORD,
    key_prefix=REDIS_RESULTS_CACHE_PREFIX,
)

GLOBAL_ASYNC_QUERIES_REDIS_CONFIG = {
    "port": REDIS_PORT,
    "host": REDIS_HOST,
    "password": REDIS_PASSWORD,
    "db": REDIS_RESULTS_DB,
}
