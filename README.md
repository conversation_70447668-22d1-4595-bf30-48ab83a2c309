# superset-plugins

Apache Superset build and Dodo specific code

See, how it works here: https://www.notion.so/dodobrands/ef0e3a54598a485db64ca882bb87d1a7

# Build & Deploy

## Текущий статус

По состоянию на 20.04.2025 используется базовый образ Superset 4.1.1:

**apache/superset:4.1.1**

При этом мы дорабатываем этот образ своими патчами на бэкенде и отдельно собираем фронтентд.

## Как это примерно происходит

Этот репозиторий генерирует нам образ.

В нём есть два GHA:

- [Build frontend](https://github.com/dodopizza/superset-plugins/blob/main/.github/workflows/build-frontend.yml)
- [Build backend](https://github.com/dodopizza/superset-plugins/blob/main/.github/workflows/build-backend.yml)

### Frontend (1 параметр)

1. Получаем фронтенд из ветки dodo-frontend репозитория [https://github.com/dodopizza/superset](https://github.com/dodopizza/superset). Имя ветки задается в параметре **Superset Frontend Branch.** Предполагаем, что ветка наследуется от текущей стабильной версии Superset (сейчас 4.1.1) и содержит специфичные для нас изменения фронтенда.
2. Билдим фронтенд с прилинкованными плагинами, записываем в образ **dodo/superset-frontend** и кладём его в наш registry.

***Внимание: чтобы увидеть изменения в деплое, после сборки фронтенда необходимо запустить сборку бэкенда!***

### Backend (1 параметр)

Для сборки бэкенда нам необходим образ фронтенда, собранный на предыдущем шаге. Дополнительно мы активно используем динамический билд питона, который не требует отдельного шага билда.

1. Получаем официальный образ.
2. Есть отдельный репозиторий [https://github.com/dodopizza/sqlalchemy-kusto](https://github.com/dodopizza/sqlalchemy-kusto), в котором мы разрабатываем диалект SQLAlchemy для Кусто. ~~В планах перевести его в OS и официально зарелизить пакет. Сейчас мы получаем его код в GHA, билдим и копируем его файлы в исходники официального образа~~ Пакет в OS. Устанавливается из PyPi.
3. Получаем бэкенд из нашей ветки репозитория [https://github.com/dodopizza/superset](https://github.com/dodopizza/superset). Имя ветки задаётся в параметре **Superset Backend Branch**. Предполагаем, что ветка наследуется от текущей стабильной версии Superset и содержит специфичные для нас изменения бэкенда, которые мы еще не успели заопенсорсить. Наша позиция - стараться не делать слишком больших изменений относительно официальной ветки из apache. Тем не менее, сейчас мы подменяем и добавляем  несколько файлов из официального образа нашими файлами: 

    [https://github.com/dodopizza/superset-plugins/blob/b60b8af4e925d52c3acd147201838bd938f8cdf4/.github/workflows/build-backend.yml#L64](https://github.com/dodopizza/superset-plugins/blob/b60b8af4e925d52c3acd147201838bd938f8cdf4/.github/workflows/build-backend.yml#L64)

4. Специфичные для нас, но не ломающие бэкенд изменения (конфиги) берутся из репозитория superset-plugins.
5. Собираем образ воедино, в том числе затягивая файлы из образа фронтеда. См. [DockerFile](https://github.com/dodopizza/superset-plugins/blob/main/Dockerfile). Записываем в образ **dodo/superset** и кладём его в наш registry.

## Deploy

Деплоим с помощью yunga из образа **dodo/superset**.

Сейчас есть три стенда:
- dev-стенд `dev dev superset` по адресу [superset.dodois.dev](https://superset.d.yandex.dodois.dev)
- dev2-стенд `dev dev superset` по адресу [superset.dodois.dev](https://spr.d.yandex.dodois.dev)
- Прод `prod we superset` по адресу [analytics.dodois.io](https://analytics.dodois.io)

## Схема

[Sign up | Miro | Online Whiteboard for Visual Collaboration](https://miro.com/app/board/o9J_l7kwBhY=/)
