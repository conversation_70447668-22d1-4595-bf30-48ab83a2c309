name: Build Dashboard Plugin [DEV]

on:
  workflow_dispatch:
    inputs:
      superset_ref:
        description: 'Superset frontend branch'
        required: true
        default: '4.1.1-dodo'
env:
  NPM_TOKEN: ${{ secrets.PACKAGES_TOKEN }}
  BUILD_NUMBER: ${{ github.run_number }}
  MICROFRONTEND_ENV_GITOPS: ${{ secrets.MICROFRONTEND_ENV_GITOPS }}
  ENTRY_POINT: supersetDashboardPlugin
  FRONTEND: officemanager
  ENVIRONMENT:

jobs:
  build-plugin-dev:
    name: Build Superset Frontend
    runs-on: ubuntu-22.04
    outputs:
      supersetDashboardPlugin: ${{ steps.publish-artifact.outputs.supersetDashboardPlugin }}
    steps:
      - name: Checkout dodopizza/superset
        uses: actions/checkout@v4.1.7
        with:
          path: repos/superset
          repository: dodopizza/superset
          ref: ${{ github.event.inputs.superset_ref }}

      - name: Print branches versions
        run: |
          echo "Superset plugins    version ${{ github.ref }}"
          echo "Superset            version ${{ github.event.inputs.superset_ref }}"

      - name: Checkout dodopizza/superset-plugins
        uses: actions/checkout@v4.1.7
        with:
          path: repos/superset-plugins

      - name: Memory Check
        working-directory: repos/superset/docker
        run: |
          sh frontend-mem-nag.sh

      - name: Use Node.js 18.19.1
        uses: actions/setup-node@v3
        with:
          registry-url: 'https://npm.pkg.github.com'
          scope: '@dodopizza'
          node-version: 18.19.1

      - name: npm ci
        working-directory: repos/superset/superset-frontend
        run: |
          npm ci

      - name: npm run lint
        working-directory: repos/superset/superset-frontend
        run: |
          npm run lint

      - name: Build superset-dashboard-plugin [DEV]
        working-directory: repos/superset/superset-frontend
        run: |
          npm run pl:build:dev
          echo "Build success"

      - name: publish artifact
        id: publish-artifact
        uses: dodopizza/create-micro-frontend-app/actions/publish@7.0.1
        with:
          artifact-dictionary: |
            ${{ env.ENTRY_POINT }} : ./repos/superset/superset-frontend/public

  deploy-dev:
    name: Deploy dev artifact
    needs: [ build-plugin-dev ]
    runs-on: ubuntu-latest
    steps:
      - name: Deploy artifact ${{ needs.build-plugin-dev.outputs.supersetDashboardPlugin }} to ${{ env.ENVIRONMENT }}
        uses: dodopizza/create-micro-frontend-app/actions/deploy@7.0.1
        with:
          microfrontend-env-gitops: ${{ env.MICROFRONTEND_ENV_GITOPS }}
          deploy-dictionary: |
            ${{ env.ENVIRONMENT }} : ${{ env.FRONTEND }} : ${{ env.ENTRY_POINT }} : ${{ needs.build-plugin-dev.outputs.supersetDashboardPlugin }}
