name: Build Frontend

on:
  workflow_dispatch:
    inputs:
      superset_ref:
        description: 'Superset frontend branch'
        required: true
        default: '4.1.1-dodo'
      superset_ui_ref:
        description: 'Superset UI branch'
        required: true
        default: 'dodo-plugins-0.17.84'

jobs:
  build-frontend:
    name: Build Superset Frontend
    runs-on: ubuntu-latest
    env:
      NPM_TOKEN: ${{ secrets.PACKAGES_TOKEN }}

    steps:
      - name: Checkout dodopizza/superset
        uses: actions/checkout@v2
        with:
          path: repos/superset
          repository: dodopizza/superset
          ref: ${{ github.event.inputs.superset_ref }}

      - name: Print branches versions
        run: |
          echo "Superset plugins    version ${{ github.ref }}"
          echo "Superset            version ${{ github.event.inputs.superset_ref }}"

      - name: Checkout dodopizza/superset-plugins
        uses: actions/checkout@v2
        with:
          path: repos/superset-plugins

      - name: Memory Check
        working-directory: repos/superset/docker
        run: |
          sh frontend-mem-nag.sh

      - name: Use Node.js 18.19.1
        uses: actions/setup-node@v3
        with:
          node-version: 18.19.1

      - name: npm install superset-frontend
        working-directory: repos/superset/superset-frontend
        run: |
          npm ci

      - name: Generate version_info.json
        working-directory: repos/superset-plugins
        run: |
          # Read version from version.json
          VERSION=$(cat version.json | jq -r '.version')
          BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
          GIT_SHA=$(git rev-parse HEAD)
          GIT_SHORT_SHA=$(git rev-parse --short HEAD)

          # Create version_info.json
          cat > version_info.json << EOF
          {
            "version": "${VERSION}",
            "build_date": "${BUILD_DATE}",
            "GIT_SHA": "${GIT_SHA}",
            "git_short_sha": "${GIT_SHORT_SHA}",
            "build_number": "${{ github.run_number }}",
            "workflow_run_id": "${{ github.run_id }}",
            "description": "Dodo Pizza Superset Custom Build"
          }
          EOF

          echo "Generated version_info.json:"
          cat version_info.json

      - name: Copy version_info.json to superset static
        run: |
          mkdir -p repos/superset/superset/static/
          cp repos/superset-plugins/version_info.json repos/superset/superset/static/version_info.json

      - name: Build Superset Frontend
        working-directory: repos/superset/superset-frontend
        run: |
          npm run build

      - name: Remove node_modules
        working-directory: repos/superset/superset-frontend
        run: |
          rm -rf node_modules

      - name: Get image name from Superset
        id: image-name
        working-directory: repos/superset
        run: |
          BRANCH_NAME=$(git symbolic-ref -q --short HEAD || git describe --tags --exact-match)
          SHORT_SHA="$(git rev-parse --short HEAD)"
          IMAGE_NAME="${BRANCH_NAME}-${SHORT_SHA}-$(date +'%N')"
          echo "Image name: ${IMAGE_NAME}"
          echo "::set-output name=image_name::$IMAGE_NAME"

      - name: Build and push docker image with tag = ${{ steps.image-name.outputs.image_name }}
        uses: dodopizza/infra.github.push-action@latest
        with:
          dockerfile: repos/superset-plugins/Dockerfile.Frontend
          image_name: superset-frontend
          image_version: ${{ steps.image-name.outputs.image_name }}
          params: ${{ secrets.INFRA_GITHUB_PUSH_ACTION }}

      - name: Build and push docker image with tag = ${{ steps.image-name.outputs.image_name }}
        uses: dodopizza/infra.github.push-action@latest
        with:
          dockerfile: repos/superset-plugins/Dockerfile.Frontend
          image_name: superset-frontend
          image_version: latest
          params: ${{ secrets.INFRA_GITHUB_PUSH_ACTION }}
