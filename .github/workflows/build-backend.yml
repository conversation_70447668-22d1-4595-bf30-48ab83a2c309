name: Build Backend

on:
  workflow_dispatch:
    inputs:
      superset_ref:
        description: 'Superset backend branch'
        required: true
        default: '4.1.1-dodo'
      stand:
        description: 'Target stand (fof, spr, or prod)'
        required: false
        type: choice
        default: 'spr'  # Default value for stand
        options:
          - fof
          - spr
          - prod
      chart_version:
        description: 'Chart version for Yunga command'
        required: false
        default: '0.1.4'  # Default value for chart_version

jobs:
  build-backend:
    name: Build Backend using base image
    runs-on: ubuntu-latest

    steps:
      - name: Checkout dodopizza/superset-plugins
        uses: actions/checkout@v2
        with:
          path: repos/superset-plugins

      - name: Checkout dodopizza/superset
        uses: actions/checkout@v2
        with:
          path: repos/superset
          repository: dodopizza/superset
          ref: ${{ github.event.inputs.superset_ref }}

      - name: Generate version_info.json for backend
        working-directory: repos/superset-plugins
        run: |
          # Read version from version.json
          VERSION=$(cat version.json | jq -r '.version')
          BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
          GIT_SHA=$(git rev-parse HEAD)
          GIT_SHORT_SHA=$(git rev-parse --short HEAD)

          # Create version_info.json
          cat > version_info.json << EOF
          {
            "version": "${VERSION}",
            "build_date": "${BUILD_DATE}",
            "GIT_SHA": "${GIT_SHA}",
            "git_short_sha": "${GIT_SHORT_SHA}",
            "build_number": "${{ github.run_number }}",
            "workflow_run_id": "${{ github.run_id }}",
            "description": "Dodo Pizza Superset Custom Build"
          }
          EOF

          echo "Generated version_info.json for backend:"
          cat version_info.json

      - name: Copy files
        working-directory: repos/superset-plugins
        run: |
          mkdir -p src/superset && cp -r ../superset/superset/* src/superset/
          # Copy version_info.json to superset static directory
          mkdir -p src/superset/static/
          cp version_info.json src/superset/static/version_info.json

      - name: Generate Image Name
        id: image-name
        working-directory: repos/superset-plugins
        run: |
          BRANCH_NAME="${GITHUB_REF#refs/heads/}"
          SHORT_SHA="$(git rev-parse --short $GITHUB_SHA)"
          IMAGE_NAME="${BRANCH_NAME}-${SHORT_SHA}-$(date +'%N')"
          echo "Image name: ${IMAGE_NAME}"
          echo "::set-output name=image_name::$IMAGE_NAME"

      - name: Build and push docker image with tag = ${{ steps.image-name.outputs.image_name }}
        uses: dodopizza/infra.github.push-action@latest
        with:
          context: repos/superset-plugins/
          image_name: superset
          image_version: ${{ steps.image-name.outputs.image_name }}
          params: ${{ secrets.INFRA_GITHUB_PUSH_ACTION }}

      - name: Print yunga command
        id: yunga-command
        run: |
          IMAGE_NAME="${{ steps.image-name.outputs.image_name }}"
          STAND="${{ github.event.inputs.stand }}"
          CHART_VERSION="${{ github.event.inputs.chart_version }}"

          if [ "$STAND" == "fof" ]; then
            YUNGA_COMMAND="/yunga promote superset d yandex fof ${IMAGE_NAME} --chart-version ${CHART_VERSION}"
          elif [ "$STAND" == "spr" ]; then
            YUNGA_COMMAND="/yunga promote superset d yandex spr ${IMAGE_NAME} --chart-version ${CHART_VERSION}"
          elif [ "$STAND" == "prod" ]; then
            YUNGA_COMMAND="/yunga promote superset p azure superset ${IMAGE_NAME} --chart-version ${CHART_VERSION}"
          else
            echo "Invalid stand value: ${STAND}"
            exit 1
          fi

          echo "Yunga Command: ${YUNGA_COMMAND}"
