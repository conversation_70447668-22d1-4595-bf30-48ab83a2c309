name: Build Dashboard Plugin [PROD]

on:
  workflow_dispatch:
    inputs:
      superset_ref:
        description: 'Superset frontend branch'
        required: true
        default: '4.1.1-dodo'
env:
  NPM_TOKEN: ${{ secrets.PACKAGES_TOKEN }}
  BUILD_NUMBER: ${{ github.run_number }}
  MICROFRONTEND_ENV_GITOPS: ${{ secrets.MICROFRONTEND_ENV_GITOPS }}
  ENTRY_POINT: supersetDashboardPlugin
  FRONTEND: officemanager
  ENVIRONMENT: we

jobs:
  build-plugin-prod:
    name: Build Superset Frontend
    runs-on: ubuntu-22.04
    outputs:
      supersetDashboardPlugin: ${{ steps.publish-artifact.outputs.supersetDashboardPlugin }}
    steps:
      - name: Checkout dodopizza/superset
        uses: actions/checkout@v4.1.7
        with:
          path: repos/superset
          repository: dodopizza/superset
          ref: ${{ github.event.inputs.superset_ref }}

      - name: Print branches versions
        run: |
          echo "Superset plugins    version ${{ github.ref }}"
          echo "Superset            version ${{ github.event.inputs.superset_ref }}"

      - name: Checkout dodopizza/superset-plugins
        uses: actions/checkout@v4.1.7
        with:
          path: repos/superset-plugins

      - name: Memory Check
        working-directory: repos/superset/docker
        run: |
          sh frontend-mem-nag.sh

      - name: Use Node.js 18.19.1
        uses: actions/setup-node@v3
        with:
          registry-url: 'https://npm.pkg.github.com'
          scope: '@dodopizza'
          node-version: 18.19.1

      - name: Generate version_info.json for plugin
        working-directory: repos/superset-plugins
        run: |
          # Read version from version.json
          VERSION=$(cat version.json | jq -r '.version')
          BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
          GIT_SHA=$(git rev-parse HEAD)
          GIT_SHORT_SHA=$(git rev-parse --short HEAD)

          # Create version_info.json
          cat > version_info.json << EOF
          {
            "version": "${VERSION}",
            "build_date": "${BUILD_DATE}",
            "GIT_SHA": "${GIT_SHA}",
            "git_short_sha": "${GIT_SHORT_SHA}",
            "build_number": "${{ github.run_number }}",
            "workflow_run_id": "${{ github.run_id }}",
            "description": "Dodo Pizza Superset Dashboard Plugin"
          }
          EOF

          echo "Generated version_info.json for plugin:"
          cat version_info.json

      - name: Copy version_info.json to superset static
        run: |
          mkdir -p repos/superset/superset/static/
          cp repos/superset-plugins/version_info.json repos/superset/superset/static/version_info.json

      - name: npm ci
        working-directory: repos/superset/superset-frontend
        run: |
          npm ci

      - name: npm run lint
        working-directory: repos/superset/superset-frontend
        run: |
          npm run lint

      - name: Build superset-dashboard-plugin [PROD]
        working-directory: repos/superset/superset-frontend
        run: |
          npm run pl:build:prod
          echo "Build success"

      - name: publish artifact
        id: publish-artifact
        uses: dodopizza/create-micro-frontend-app/actions/publish@7.0.1
        with:
          artifact-dictionary: |
            ${{ env.ENTRY_POINT }} : ./repos/superset/superset-frontend/public

  deploy-prod:
    name: Deploy prod artifact
    needs: [ build-plugin-prod ]
    runs-on: ubuntu-latest
    steps:
      - name: Deploy artifact ${{ needs.build-plugin-prod.outputs.supersetDashboardPlugin }} to ${{ env.ENVIRONMENT }}
        uses: dodopizza/create-micro-frontend-app/actions/deploy@7.0.1
        with:
          microfrontend-env-gitops: ${{ env.MICROFRONTEND_ENV_GITOPS }}
          deploy-dictionary: |
            ${{ env.ENVIRONMENT }} : ${{ env.FRONTEND }} : ${{ env.ENTRY_POINT }} : ${{ needs.build-plugin-prod.outputs.supersetDashboardPlugin }}
